#!/usr/bin/env python3
"""
Test script to verify enhanced memory cleanup functionality.
This script tests the improved memory management with stricter recovery thresholds.
"""

import sys
import os
import gc
import psutil
import logging
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import config
import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_memory_usage():
    """Get current memory usage in MB"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # Convert to MB
    except Exception as e:
        logger.warning(f"Could not get memory usage: {e}")
        return 0

def get_system_memory_info():
    """Get system memory information"""
    try:
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / 1024 / 1024,  # MB
            'available': memory.available / 1024 / 1024,  # MB
            'percent': memory.percent,
            'used': memory.used / 1024 / 1024  # MB
        }
    except Exception as e:
        logger.warning(f"Could not get system memory info: {e}")
        return {'total': 0, 'available': 0, 'percent': 0, 'used': 0}

def force_garbage_collection(aggressive: bool = False):
    """Force garbage collection to free up memory"""
    try:
        memory_before = get_memory_usage()
        
        # Force garbage collection multiple times for better cleanup
        collected_total = 0
        cycles = 5 if aggressive else 3
        
        for i in range(cycles):
            collected = gc.collect()
            collected_total += collected
            if collected == 0 and i > 0:  # Allow at least one cycle
                break
        
        memory_after = get_memory_usage()
        memory_freed = memory_before - memory_after
        
        logger.info(f"Garbage collection (aggressive={aggressive}) freed {collected_total} objects across {i+1} cycles, "
                   f"memory: {memory_before:.2f}MB → {memory_after:.2f}MB (freed {memory_freed:.2f}MB)")
        return collected_total
    except Exception as e:
        logger.warning(f"Error during garbage collection: {e}")
        return 0

def complete_memory_cleanup():
    """Enhanced complete memory cleanup"""
    try:
        memory_before = get_memory_usage()
        system_before = get_system_memory_info()
        
        logger.info(f"Starting enhanced memory cleanup - Process: {memory_before:.2f}MB, System: {system_before['percent']:.1f}%")
        
        # Multiple aggressive garbage collection cycles with more thorough cleanup
        total_collected = 0
        for cycle in range(5):  # Increased cycles
            collected = force_garbage_collection(aggressive=True)
            total_collected += collected
            if collected == 0 and cycle > 1:
                break
        
        # More comprehensive cleanup
        import ctypes
        
        # Clear type cache
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()
        
        # Clear import cache for modules that might be holding references
        if hasattr(sys, 'modules'):
            temp_modules = [mod for mod in sys.modules.keys() if 'temp' in mod.lower() or 'cache' in mod.lower()]
            for mod in temp_modules:
                try:
                    del sys.modules[mod]
                except:
                    pass
        
        # Force Python to release memory back to OS (if possible)
        try:
            if hasattr(ctypes, 'CDLL'):
                libc = ctypes.CDLL("libc.so.6")
                if hasattr(libc, 'malloc_trim'):
                    libc.malloc_trim(0)
        except:
            pass  # Not available on all systems
        
        # Final garbage collection after all cleanup
        final_collected = force_garbage_collection(aggressive=True)
        total_collected += final_collected
        
        memory_after = get_memory_usage()
        system_after = get_system_memory_info()
        
        memory_freed = memory_before - memory_after
        system_freed = system_before['percent'] - system_after['percent']
        
        cleanup_stats = {
            'memory_before_mb': memory_before,
            'memory_after_mb': memory_after,
            'memory_freed_mb': memory_freed,
            'system_before_percent': system_before['percent'],
            'system_after_percent': system_after['percent'],
            'system_freed_percent': system_freed,
            'objects_collected': total_collected,
            'cleanup_effective': memory_freed > 0 or total_collected > 0,
            'cleanup_cycles': cycle + 1
        }
        
        logger.info(f"Enhanced memory cleanup finished - Process: {memory_before:.2f}MB → {memory_after:.2f}MB "
                   f"(freed {memory_freed:.2f}MB), System: {system_before['percent']:.1f}% → {system_after['percent']:.1f}% "
                   f"(freed {system_freed:.1f}%), Objects: {total_collected} across {cycle + 1} cycles")
        
        return cleanup_stats
    except Exception as e:
        logger.error(f"Error during enhanced memory cleanup: {e}")
        return None

def test_enhanced_memory_recovery():
    """Test enhanced memory recovery with stricter thresholds"""
    logger.info("🚀 Starting enhanced memory recovery test...")
    
    # Record initial state
    initial_memory = get_memory_usage()
    initial_system = get_system_memory_info()
    
    logger.info(f"📊 Initial state - Process: {initial_memory:.2f}MB, System: {initial_system['percent']:.1f}%")
    
    # Create significant memory usage
    logger.info("🔄 Creating significant memory usage...")
    data_structures = []
    
    try:
        # Create larger data structures to test enhanced cleanup
        for i in range(10):
            # Create larger chunks - 20MB each
            large_list = [f"item_{j}" * 100 for j in range(20000)]
            large_dict = {f"key_{j}": f"value_{j}" * 200 for j in range(5000)}
            large_string = "x" * 500000  # 500KB string
            
            data_structures.append({
                'list': large_list,
                'dict': large_dict,
                'string': large_string,
                'nested': {'data': [large_dict.copy() for _ in range(5)]}
            })
            
            current_memory = get_memory_usage()
            logger.info(f"   Created structure {i+1}/10 - Memory: {current_memory:.2f}MB")
        
        # Check peak memory usage
        peak_memory = get_memory_usage()
        peak_system = get_system_memory_info()
        
        logger.info(f"📈 Peak usage - Process: {peak_memory:.2f}MB (+{peak_memory-initial_memory:.2f}MB), "
                   f"System: {peak_system['percent']:.1f}% (+{peak_system['percent']-initial_system['percent']:.1f}%)")
        
        # Now test enhanced cleanup
        logger.info("🧹 Testing enhanced memory cleanup...")
        
        # Clear the data structures
        data_structures.clear()
        del data_structures
        
        # Perform enhanced cleanup
        cleanup_stats = complete_memory_cleanup()
        
        # Check final memory state
        final_memory = get_memory_usage()
        final_system = get_system_memory_info()
        
        # Calculate recovery
        memory_recovery = peak_memory - final_memory
        system_recovery = peak_system['percent'] - final_system['percent']
        memory_change = final_memory - initial_memory
        
        # Log comprehensive results
        logger.info(f"🏁 Enhanced Memory Recovery Test Results:")
        logger.info(f"   📊 Process Memory: {initial_memory:.2f}MB → {peak_memory:.2f}MB → {final_memory:.2f}MB")
        logger.info(f"   📈 Peak increase: +{peak_memory-initial_memory:.2f}MB")
        logger.info(f"   📉 Recovery: -{memory_recovery:.2f}MB")
        logger.info(f"   🎯 Final vs Initial: {memory_change:+.2f}MB")
        logger.info(f"   🖥️  System Memory: {initial_system['percent']:.1f}% → {peak_system['percent']:.1f}% → {final_system['percent']:.1f}%")
        logger.info(f"   🗑️  Cleanup Stats: {cleanup_stats}")
        
        # Evaluate success with stricter thresholds
        excellent_recovery = abs(memory_change) < 20  # Within 20MB is excellent
        good_recovery = abs(memory_change) < 50  # Within 50MB is good
        system_recovered_well = abs(final_system['percent'] - initial_system['percent']) < 1  # Within 1%
        
        if excellent_recovery and system_recovered_well:
            logger.info("✅ Enhanced memory cleanup test EXCELLENT - Memory returned to near initial state")
            recovery_status = "excellent"
        elif good_recovery and system_recovered_well:
            logger.info("✅ Enhanced memory cleanup test PASSED - Memory returned to acceptable range")
            recovery_status = "success"
        else:
            logger.warning("⚠️ Enhanced memory cleanup test PARTIAL - Some memory not recovered")
            recovery_status = "incomplete"
            if not good_recovery:
                logger.warning(f"   Process memory difference: {memory_change:+.2f}MB (excellent: ±20MB, good: ±50MB)")
            if not system_recovered_well:
                logger.warning(f"   System memory difference: {final_system['percent']-initial_system['percent']:+.1f}% (threshold: ±1%)")
        
        return {
            'recovery_status': recovery_status,
            'memory_change': memory_change,
            'cleanup_stats': cleanup_stats,
            'excellent_recovery': excellent_recovery,
            'good_recovery': good_recovery
        }
        
    except Exception as e:
        logger.error(f"❌ Error during enhanced memory test: {e}")
        return None

def main():
    """Main test function"""
    logger.info("Starting enhanced memory cleanup tests...")
    
    # Test enhanced memory recovery
    logger.info("\n=== Enhanced Memory Recovery Test ===")
    recovery_result = test_enhanced_memory_recovery()
    
    if recovery_result:
        logger.info(f"\n📋 Test Summary:")
        logger.info(f"   Recovery Status: {recovery_result['recovery_status']}")
        logger.info(f"   Memory Change: {recovery_result['memory_change']:+.2f}MB")
        logger.info(f"   Excellent Recovery: {recovery_result['excellent_recovery']}")
        logger.info(f"   Good Recovery: {recovery_result['good_recovery']}")
    
    logger.info("\n✅ Enhanced memory cleanup tests completed!")

if __name__ == "__main__":
    main()
