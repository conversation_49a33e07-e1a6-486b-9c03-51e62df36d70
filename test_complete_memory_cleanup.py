#!/usr/bin/env python3
"""
Test script to verify complete memory cleanup functionality.
This script tests the enhanced memory management and cleanup functions.
"""

import sys
import os
import gc
import psutil
import logging
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import config
import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_memory_usage():
    """Get current memory usage in MB"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # Convert to MB
    except Exception as e:
        logger.warning(f"Could not get memory usage: {e}")
        return 0

def get_system_memory_info():
    """Get system memory information"""
    try:
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / 1024 / 1024,  # MB
            'available': memory.available / 1024 / 1024,  # MB
            'percent': memory.percent,
            'used': memory.used / 1024 / 1024  # MB
        }
    except Exception as e:
        logger.warning(f"Could not get system memory info: {e}")
        return {'total': 0, 'available': 0, 'percent': 0, 'used': 0}

def force_garbage_collection(aggressive: bool = False):
    """Force garbage collection to free up memory"""
    try:
        memory_before = get_memory_usage()
        
        # Force garbage collection multiple times for better cleanup
        collected_total = 0
        cycles = 5 if aggressive else 3
        
        for i in range(cycles):
            collected = gc.collect()
            collected_total += collected
            if collected == 0 and i > 0:  # Allow at least one cycle
                break
        
        memory_after = get_memory_usage()
        memory_freed = memory_before - memory_after
        
        logger.info(f"Garbage collection (aggressive={aggressive}) freed {collected_total} objects across {i+1} cycles, "
                   f"memory: {memory_before:.2f}MB → {memory_after:.2f}MB (freed {memory_freed:.2f}MB)")
        return collected_total
    except Exception as e:
        logger.warning(f"Error during garbage collection: {e}")
        return 0

def complete_memory_cleanup():
    """Perform complete memory cleanup and return memory statistics"""
    try:
        memory_before = get_memory_usage()
        system_before = get_system_memory_info()
        
        logger.info(f"Starting complete memory cleanup - Process: {memory_before:.2f}MB, System: {system_before['percent']:.1f}%")
        
        # Multiple aggressive garbage collection cycles
        total_collected = 0
        for cycle in range(3):
            collected = force_garbage_collection(aggressive=True)
            total_collected += collected
            if collected == 0:
                break
        
        # Clear any remaining references
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()
        
        memory_after = get_memory_usage()
        system_after = get_system_memory_info()
        
        memory_freed = memory_before - memory_after
        system_freed = system_before['percent'] - system_after['percent']
        
        cleanup_stats = {
            'memory_before_mb': memory_before,
            'memory_after_mb': memory_after,
            'memory_freed_mb': memory_freed,
            'system_before_percent': system_before['percent'],
            'system_after_percent': system_after['percent'],
            'system_freed_percent': system_freed,
            'objects_collected': total_collected,
            'cleanup_effective': memory_freed > 0
        }
        
        logger.info(f"Complete memory cleanup finished - Process: {memory_before:.2f}MB → {memory_after:.2f}MB "
                   f"(freed {memory_freed:.2f}MB), System: {system_before['percent']:.1f}% → {system_after['percent']:.1f}% "
                   f"(freed {system_freed:.1f}%), Objects: {total_collected}")
        
        return cleanup_stats
    except Exception as e:
        logger.error(f"Error during complete memory cleanup: {e}")
        return {
            'memory_before_mb': 0,
            'memory_after_mb': 0,
            'memory_freed_mb': 0,
            'system_before_percent': 0,
            'system_after_percent': 0,
            'system_freed_percent': 0,
            'objects_collected': 0,
            'cleanup_effective': False,
            'error': str(e)
        }

def simulate_memory_usage_and_cleanup():
    """Simulate memory usage and test cleanup"""
    logger.info("🚀 Starting memory usage simulation and cleanup test...")
    
    # Record initial state
    initial_memory = get_memory_usage()
    initial_system = get_system_memory_info()
    
    logger.info(f"📊 Initial state - Process: {initial_memory:.2f}MB, System: {initial_system['percent']:.1f}%")
    
    # Create some memory usage
    logger.info("🔄 Creating memory usage...")
    data_structures = []
    
    try:
        # Create various data structures to simulate real usage
        for i in range(5):
            # Create lists, dicts, and strings
            large_list = [f"item_{j}" for j in range(10000)]
            large_dict = {f"key_{j}": f"value_{j}" * 100 for j in range(1000)}
            large_string = "x" * 100000
            
            data_structures.append({
                'list': large_list,
                'dict': large_dict,
                'string': large_string,
                'nested': {'data': [large_dict.copy() for _ in range(10)]}
            })
            
            current_memory = get_memory_usage()
            logger.info(f"   Created structure {i+1}/5 - Memory: {current_memory:.2f}MB")
        
        # Check peak memory usage
        peak_memory = get_memory_usage()
        peak_system = get_system_memory_info()
        
        logger.info(f"📈 Peak usage - Process: {peak_memory:.2f}MB (+{peak_memory-initial_memory:.2f}MB), "
                   f"System: {peak_system['percent']:.1f}% (+{peak_system['percent']-initial_system['percent']:.1f}%)")
        
        # Now test cleanup
        logger.info("🧹 Testing memory cleanup...")
        
        # Clear the data structures
        data_structures.clear()
        del data_structures
        
        # Perform complete cleanup
        cleanup_stats = complete_memory_cleanup()
        
        # Check final memory state
        final_memory = get_memory_usage()
        final_system = get_system_memory_info()
        
        # Calculate recovery
        memory_recovery = peak_memory - final_memory
        system_recovery = peak_system['percent'] - final_system['percent']
        
        # Log comprehensive results
        logger.info(f"🏁 Memory Recovery Test Results:")
        logger.info(f"   📊 Process Memory: {initial_memory:.2f}MB → {peak_memory:.2f}MB → {final_memory:.2f}MB")
        logger.info(f"   📈 Peak increase: +{peak_memory-initial_memory:.2f}MB")
        logger.info(f"   📉 Recovery: -{memory_recovery:.2f}MB")
        logger.info(f"   🎯 Final vs Initial: {final_memory-initial_memory:+.2f}MB")
        logger.info(f"   🖥️  System Memory: {initial_system['percent']:.1f}% → {peak_system['percent']:.1f}% → {final_system['percent']:.1f}%")
        logger.info(f"   🗑️  Cleanup Stats: {cleanup_stats}")
        
        # Evaluate success
        memory_recovered_well = abs(final_memory - initial_memory) < 20  # Within 20MB
        system_recovered_well = abs(final_system['percent'] - initial_system['percent']) < 1  # Within 1%
        
        if memory_recovered_well and system_recovered_well:
            logger.info("✅ Memory cleanup test PASSED - Memory returned to near initial state")
        else:
            logger.warning("⚠️ Memory cleanup test PARTIAL - Some memory not recovered")
            if not memory_recovered_well:
                logger.warning(f"   Process memory difference: {final_memory-initial_memory:+.2f}MB (threshold: ±20MB)")
            if not system_recovered_well:
                logger.warning(f"   System memory difference: {final_system['percent']-initial_system['percent']:+.1f}% (threshold: ±1%)")
        
        return cleanup_stats
        
    except Exception as e:
        logger.error(f"❌ Error during memory simulation: {e}")
        return None

def main():
    """Main test function"""
    logger.info("Starting complete memory cleanup tests...")
    
    # Test 1: Basic memory functions
    logger.info("\n=== Test 1: Basic Memory Functions ===")
    initial_memory = get_memory_usage()
    system_memory = get_system_memory_info()
    logger.info(f"Current memory - Process: {initial_memory:.2f}MB, System: {system_memory['percent']:.1f}%")
    
    # Test 2: Garbage collection
    logger.info("\n=== Test 2: Garbage Collection ===")
    collected = force_garbage_collection(aggressive=False)
    logger.info(f"Normal GC collected {collected} objects")
    
    collected_aggressive = force_garbage_collection(aggressive=True)
    logger.info(f"Aggressive GC collected {collected_aggressive} objects")
    
    # Test 3: Complete cleanup
    logger.info("\n=== Test 3: Complete Memory Cleanup ===")
    cleanup_stats = complete_memory_cleanup()
    logger.info(f"Complete cleanup stats: {cleanup_stats}")
    
    # Test 4: Memory usage simulation
    logger.info("\n=== Test 4: Memory Usage Simulation ===")
    simulation_stats = simulate_memory_usage_and_cleanup()
    
    logger.info("\n✅ Complete memory cleanup tests finished!")

if __name__ == "__main__":
    main()
