# Memory Management Fixes for <PERSON><PERSON> Extractor and Validator

## Problem Analysis
The MCQ extractor and validator API was experiencing out-of-memory errors (SIGKILL) due to:
1. Low memory limits (320MB) insufficient for PDF processing
2. High PDF zoom factor (2.0x) creating large images
3. Too many concurrent operations (3 extractions, 3 validations)
4. Insufficient memory monitoring and cleanup
5. Large image processing operations without proper memory management

## Solutions Implemented

### 1. Configuration Updates (`config.py`)
- **Increased memory limits**:
  - `MAX_MEMORY_MB`: 320 → 1024 MB (1GB)
  - Added `CRITICAL_MEMORY_MB`: 1280 MB (1.25GB) - hard stop threshold
  - Added `SAFE_MEMORY_MB`: 768 MB - safe operation threshold
- **Reduced concurrent operations**:
  - `MAX_CONCURRENT_EXTRACTIONS`: 3 → 2
  - Added `MAX_CONCURRENT_VALIDATIONS`: 2
- **Optimized processing settings**:
  - `PDF_ZOOM_FACTOR`: 2.0 → 1.5 (reduces image size by ~44%)
  - `EXTRACTION_TIMEOUT_SECONDS`: 900 → 600 (10 minutes)
  - `FUTURE_TIMEOUT_SECONDS`: 1200 → 900 (15 minutes)
- **Added memory management controls**:
  - `MEMORY_CHECK_INTERVAL`: 3 (check every 3 completed tasks)
  - `FORCE_GC_THRESHOLD`: 0.8 (force cleanup at 80% of max memory)

### 2. Enhanced Memory Management Functions (`agents/mcq_extractor.py`)
- **Improved memory monitoring**:
  - Added `get_system_memory_info()` for system-wide memory tracking
  - Enhanced `check_memory_limit()` with multiple thresholds and critical checks
  - Improved `force_garbage_collection()` with multiple cleanup cycles
- **Memory status logging**:
  - Added `log_memory_status()` method for detailed memory reporting
  - Integrated memory logging at key processing points

### 3. MCQExtractor Class Improvements
- **Constructor updates**:
  - Uses config-based defaults for all memory-related settings
  - Logs initial memory status and configuration
- **Enhanced processing**:
  - Memory checks before and after PDF conversion
  - Configurable memory check intervals during parallel processing
  - Graceful task cancellation when memory limits are exceeded
  - Enhanced cleanup with explicit variable deletion

### 4. PDF Processing Optimizations (`agents/utils/pdf_helpers.py`)
- **Reduced memory footprint**:
  - Uses configurable zoom factor (default 1.5 instead of 2.0)
  - Better memory management during PDF to image conversion

### 5. MCQ Validator Updates (`agents/mcq_validator.py`)
- **Reduced concurrency**:
  - Uses config-based concurrent validation limit (default 2)
  - Better resource management during parallel validation

### 6. Service Layer Updates (`services/mcq_extractor_validator_service.py`)
- **Timeout management**:
  - Uses config-based timeout values for consistency
  - Better error reporting with actual timeout values

## Memory Management Strategy

### Three-Tier Memory Thresholds
1. **Safe Zone** (< 768 MB): Normal operation
2. **Warning Zone** (768-1024 MB): Force garbage collection
3. **Critical Zone** (> 1024 MB): Stop processing, cleanup, and error

### Progressive Cleanup
1. **Regular cleanup**: Every 3 completed tasks
2. **Forced cleanup**: When memory exceeds 80% of limit
3. **Emergency cleanup**: When approaching critical threshold
4. **Final cleanup**: Always performed at completion

### Concurrent Operation Limits
- **PDF Processing**: Single-threaded to avoid memory spikes
- **MCQ Extraction**: Maximum 2 concurrent threads
- **MCQ Validation**: Maximum 2 concurrent validations
- **Image Processing**: Reduced zoom factor and better cleanup

## Testing and Verification

### Test Script (`test_memory_management.py`)
- Tests all memory management functions
- Verifies configuration values
- Tests MCQExtractor initialization
- Optional memory pressure simulation

### Usage
```bash
python test_memory_management.py
```

## Expected Results

### Memory Usage Reduction
- **PDF Images**: ~44% smaller due to reduced zoom factor
- **Peak Memory**: Better controlled with progressive cleanup
- **System Impact**: Reduced system memory pressure

### Improved Stability
- **Graceful degradation**: Tasks cancelled before system limits
- **Better error handling**: Memory errors caught and reported
- **Recovery capability**: System can recover from memory pressure

### Performance Considerations
- **Slightly longer processing**: Due to reduced concurrency
- **Better reliability**: Fewer crashes and restarts
- **Consistent performance**: More predictable memory usage

## Monitoring and Maintenance

### Log Messages to Monitor
- Memory status reports at key processing points
- Memory limit warnings and critical alerts
- Task cancellation due to memory constraints
- Garbage collection effectiveness

### Key Metrics
- Process memory usage (should stay < 1GB)
- System memory percentage (should stay < 90%)
- Task completion rates vs. cancellation rates
- Garbage collection frequency and effectiveness

## Rollback Plan
If issues occur, revert these files to previous versions:
1. `config.py` - restore original memory limits
2. `agents/mcq_extractor.py` - restore original memory functions
3. `agents/mcq_validator.py` - restore original concurrency
4. `agents/utils/pdf_helpers.py` - restore original zoom factor
5. `services/mcq_extractor_validator_service.py` - restore original timeouts

## Future Improvements
1. **Dynamic memory management**: Adjust concurrency based on available memory
2. **Memory pooling**: Reuse memory allocations for better efficiency
3. **Streaming processing**: Process large files in chunks
4. **External monitoring**: Integration with system monitoring tools
