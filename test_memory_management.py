#!/usr/bin/env python3
"""
Test script to verify memory management improvements in MCQ extractor.
This script tests the memory monitoring functions and configuration.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the memory management functions
from agents.mcq_extractor import (
    get_memory_usage, 
    get_system_memory_info, 
    force_garbage_collection, 
    check_memory_limit,
    MCQExtractor
)
import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_memory_functions():
    """Test the memory management functions"""
    logger.info("Testing memory management functions...")
    
    # Test memory usage function
    memory_usage = get_memory_usage()
    logger.info(f"Current process memory usage: {memory_usage:.2f} MB")
    
    # Test system memory info
    system_memory = get_system_memory_info()
    logger.info(f"System memory - Total: {system_memory['total']:.2f} MB, "
               f"Used: {system_memory['percent']:.1f}%, Available: {system_memory['available']:.2f} MB")
    
    # Test garbage collection
    collected = force_garbage_collection()
    logger.info(f"Garbage collection freed {collected} objects")
    
    # Test memory limit checking
    try:
        current_memory = check_memory_limit()
        logger.info(f"Memory check passed - Current usage: {current_memory:.2f} MB")
    except MemoryError as e:
        logger.error(f"Memory limit exceeded: {e}")

def test_config_values():
    """Test the new configuration values"""
    logger.info("Testing configuration values...")
    
    logger.info(f"MAX_MEMORY_MB: {getattr(config, 'MAX_MEMORY_MB', 'Not set')}")
    logger.info(f"CRITICAL_MEMORY_MB: {getattr(config, 'CRITICAL_MEMORY_MB', 'Not set')}")
    logger.info(f"SAFE_MEMORY_MB: {getattr(config, 'SAFE_MEMORY_MB', 'Not set')}")
    logger.info(f"MAX_CONCURRENT_EXTRACTIONS: {getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 'Not set')}")
    logger.info(f"MAX_CONCURRENT_VALIDATIONS: {getattr(config, 'MAX_CONCURRENT_VALIDATIONS', 'Not set')}")
    logger.info(f"PDF_ZOOM_FACTOR: {getattr(config, 'PDF_ZOOM_FACTOR', 'Not set')}")
    logger.info(f"MEMORY_CHECK_INTERVAL: {getattr(config, 'MEMORY_CHECK_INTERVAL', 'Not set')}")
    logger.info(f"FORCE_GC_THRESHOLD: {getattr(config, 'FORCE_GC_THRESHOLD', 'Not set')}")

def test_mcq_extractor_initialization():
    """Test MCQExtractor initialization with new settings"""
    logger.info("Testing MCQExtractor initialization...")
    
    try:
        extractor = MCQExtractor()
        logger.info(f"MCQExtractor initialized successfully")
        logger.info(f"Max concurrent extractions: {extractor.max_concurrent_extractions}")
        logger.info(f"Memory check interval: {extractor.memory_check_interval}")
        logger.info(f"PDF zoom factor: {extractor.pdf_zoom_factor}")
        
        # Test memory status logging
        extractor.log_memory_status("during test")
        
    except Exception as e:
        logger.error(f"Error initializing MCQExtractor: {e}")

def simulate_memory_pressure():
    """Simulate memory pressure to test memory management"""
    logger.info("Simulating memory pressure...")
    
    try:
        # Create some memory pressure (be careful not to crash the system)
        data_list = []
        for i in range(10):
            # Create 10MB chunks
            chunk = bytearray(10 * 1024 * 1024)  # 10MB
            data_list.append(chunk)
            
            memory_usage = get_memory_usage()
            logger.info(f"Created chunk {i+1}, memory usage: {memory_usage:.2f} MB")
            
            # Test memory checking
            try:
                check_memory_limit()
            except MemoryError as e:
                logger.warning(f"Memory limit reached at chunk {i+1}: {e}")
                break
        
        # Clean up
        del data_list
        force_garbage_collection()
        logger.info("Memory pressure simulation completed and cleaned up")
        
    except Exception as e:
        logger.error(f"Error during memory pressure simulation: {e}")

def main():
    """Main test function"""
    logger.info("Starting memory management tests...")
    
    # Test 1: Basic memory functions
    test_memory_functions()
    
    # Test 2: Configuration values
    test_config_values()
    
    # Test 3: MCQExtractor initialization
    test_mcq_extractor_initialization()
    
    # Test 4: Memory pressure simulation (optional, can be commented out)
    # simulate_memory_pressure()
    
    logger.info("Memory management tests completed!")

if __name__ == "__main__":
    main()
