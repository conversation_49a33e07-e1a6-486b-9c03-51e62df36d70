#!/usr/bin/env python3
"""
Simple test script to verify memory management configuration changes.
This script only tests configuration and basic memory functions without dependencies.
"""

import sys
import os
import gc
import psutil
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import config
import config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_memory_usage():
    """Get current memory usage in MB"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # Convert to MB
    except Exception as e:
        logger.warning(f"Could not get memory usage: {e}")
        return 0

def get_system_memory_info():
    """Get system memory information"""
    try:
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / 1024 / 1024,  # MB
            'available': memory.available / 1024 / 1024,  # MB
            'percent': memory.percent,
            'used': memory.used / 1024 / 1024  # MB
        }
    except Exception as e:
        logger.warning(f"Could not get system memory info: {e}")
        return {'total': 0, 'available': 0, 'percent': 0, 'used': 0}

def force_garbage_collection():
    """Force garbage collection to free up memory"""
    try:
        collected_total = 0
        for i in range(3):
            collected = gc.collect()
            collected_total += collected
            if collected == 0:
                break
        
        logger.info(f"Garbage collection freed {collected_total} objects across {i+1} cycles")
        return collected_total
    except Exception as e:
        logger.warning(f"Error during garbage collection: {e}")
        return 0

def test_memory_functions():
    """Test the memory management functions"""
    logger.info("Testing memory management functions...")
    
    # Test memory usage function
    memory_usage = get_memory_usage()
    logger.info(f"Current process memory usage: {memory_usage:.2f} MB")
    
    # Test system memory info
    system_memory = get_system_memory_info()
    logger.info(f"System memory - Total: {system_memory['total']:.2f} MB, "
               f"Used: {system_memory['percent']:.1f}%, Available: {system_memory['available']:.2f} MB")
    
    # Test garbage collection
    collected = force_garbage_collection()
    logger.info(f"Garbage collection freed {collected} objects")

def test_config_values():
    """Test the new configuration values"""
    logger.info("Testing configuration values...")
    
    # Test memory settings
    max_memory = getattr(config, 'MAX_MEMORY_MB', None)
    critical_memory = getattr(config, 'CRITICAL_MEMORY_MB', None)
    safe_memory = getattr(config, 'SAFE_MEMORY_MB', None)
    
    logger.info(f"MAX_MEMORY_MB: {max_memory}")
    logger.info(f"CRITICAL_MEMORY_MB: {critical_memory}")
    logger.info(f"SAFE_MEMORY_MB: {safe_memory}")
    
    # Test concurrency settings
    max_extractions = getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', None)
    max_validations = getattr(config, 'MAX_CONCURRENT_VALIDATIONS', None)
    
    logger.info(f"MAX_CONCURRENT_EXTRACTIONS: {max_extractions}")
    logger.info(f"MAX_CONCURRENT_VALIDATIONS: {max_validations}")
    
    # Test processing settings
    pdf_zoom = getattr(config, 'PDF_ZOOM_FACTOR', None)
    memory_check_interval = getattr(config, 'MEMORY_CHECK_INTERVAL', None)
    force_gc_threshold = getattr(config, 'FORCE_GC_THRESHOLD', None)
    
    logger.info(f"PDF_ZOOM_FACTOR: {pdf_zoom}")
    logger.info(f"MEMORY_CHECK_INTERVAL: {memory_check_interval}")
    logger.info(f"FORCE_GC_THRESHOLD: {force_gc_threshold}")
    
    # Test timeout settings
    extraction_timeout = getattr(config, 'EXTRACTION_TIMEOUT_SECONDS', None)
    future_timeout = getattr(config, 'FUTURE_TIMEOUT_SECONDS', None)
    
    logger.info(f"EXTRACTION_TIMEOUT_SECONDS: {extraction_timeout}")
    logger.info(f"FUTURE_TIMEOUT_SECONDS: {future_timeout}")

def validate_config_values():
    """Validate that configuration values are reasonable"""
    logger.info("Validating configuration values...")
    
    issues = []
    
    # Check memory limits
    max_memory = getattr(config, 'MAX_MEMORY_MB', 0)
    critical_memory = getattr(config, 'CRITICAL_MEMORY_MB', 0)
    safe_memory = getattr(config, 'SAFE_MEMORY_MB', 0)
    
    if max_memory < 512:
        issues.append(f"MAX_MEMORY_MB ({max_memory}) might be too low for PDF processing")
    
    if critical_memory <= max_memory:
        issues.append(f"CRITICAL_MEMORY_MB ({critical_memory}) should be higher than MAX_MEMORY_MB ({max_memory})")
    
    if safe_memory >= max_memory:
        issues.append(f"SAFE_MEMORY_MB ({safe_memory}) should be lower than MAX_MEMORY_MB ({max_memory})")
    
    # Check concurrency limits
    max_extractions = getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 0)
    max_validations = getattr(config, 'MAX_CONCURRENT_VALIDATIONS', 0)
    
    if max_extractions > 4:
        issues.append(f"MAX_CONCURRENT_EXTRACTIONS ({max_extractions}) might be too high for memory safety")
    
    if max_validations > 4:
        issues.append(f"MAX_CONCURRENT_VALIDATIONS ({max_validations}) might be too high for memory safety")
    
    # Check PDF zoom factor
    pdf_zoom = getattr(config, 'PDF_ZOOM_FACTOR', 0)
    if pdf_zoom > 2.0:
        issues.append(f"PDF_ZOOM_FACTOR ({pdf_zoom}) might be too high and cause memory issues")
    
    # Report results
    if issues:
        logger.warning("Configuration validation issues found:")
        for issue in issues:
            logger.warning(f"  - {issue}")
    else:
        logger.info("All configuration values look reasonable!")

def main():
    """Main test function"""
    logger.info("Starting memory management configuration tests...")
    
    # Test 1: Basic memory functions
    test_memory_functions()
    
    # Test 2: Configuration values
    test_config_values()
    
    # Test 3: Validate configuration
    validate_config_values()
    
    logger.info("Memory management configuration tests completed!")

if __name__ == "__main__":
    main()
